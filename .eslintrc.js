module.exports = {
  root: true,
  parser: "@typescript-eslint/parser",
  parserOptions: {
    ecmaVersion: 2020,
    sourceType: "module",
  },
  plugins: ["@typescript-eslint"],
  extends: ["eslint:recommended"],
  env: {
    node: true,
    es6: true,
  },
  rules: {
    "spaced-comment": ["error", "always"],
    "@typescript-eslint/indent": ["error", 4],
    "no-redeclare": "off", // Turn off base rule
    "@typescript-eslint/no-redeclare": "error",
    "no-eval": "error",
    "@typescript-eslint/prefer-namespace-keyword": "error",
    "no-trailing-spaces": "error",
    "no-var": "error",
    "brace-style": ["error", "1tbs"],
    quotes: "off",
    semi: "off", // Turn off base rule
    "@typescript-eslint/semi": ["error", "always"],
    eqeqeq: ["error", "always", { null: "ignore" }],
    "@typescript-eslint/type-annotation-spacing": [
      "error",
      {
        before: false,
        after: true,
        overrides: {
          arrow: { before: true, after: true },
        },
      },
    ],
    "@typescript-eslint/naming-convention": [
      "error",
      {
        selector: "variable",
        format: ["camelCase", "PascalCase", "UPPER_CASE"],
        filter: {
          regex:
            "^(break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|false|finally|for|function|if|import|in|instanceof|new|null|return|super|switch|this|throw|true|try|typeof|var|void|while|with)$",
          match: false,
        },
      },
      {
        selector: "class",
        format: ["PascalCase"],
      },
    ],
    "space-before-blocks": "error",
    "keyword-spacing": "error",
    "space-infix-ops": "error",
    "space-before-function-paren": [
      "error",
      {
        anonymous: "always",
        named: "never",
        asyncArrow: "always",
      },
    ],
    "object-curly-spacing": ["error", "always"],
    "array-bracket-spacing": ["error", "never"],
    "computed-property-spacing": ["error", "never"],
  },
  ignorePatterns: ["lib/**/*", "node_modules/**/*", "coverage/**/*"],
};
