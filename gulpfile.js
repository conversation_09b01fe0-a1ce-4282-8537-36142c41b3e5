"use strict";

var gulp        = require('gulp');
var mocha       = require('gulp-mocha');


var runSequence = require('run-sequence');

var istanbul = require('gulp-istanbul');
var remapIstanbul = require('remap-istanbul/lib/gulpRemapIstanbul');


gulp.task('pre-test', function () {
    return gulp.src(['lib/skywind/**/*.js', 'lib/index.js'])
    // Covering files
        .pipe(istanbul({includeUntested: true}))
        // Force `require` to return covered files
        .pipe(istanbul.hookRequire());
});

gulp.task('test-server', ['pre-test'], () => {
    return gulp.src(['lib/test/**/*.spec.js'])
        .pipe(mocha({
            "reporter": "mocha-jenkins-reporter",
            "reporterOptions": {
                "junit_report_name": "Tests",
                "junit_report_path": "coverage/xunit.xml",
                "junit_report_stack": 1
            }}))
        .pipe(istanbul.writeReports());
});

gulp.task('remap-istanbul', function () {
    return gulp.src('./coverage/coverage-final.json', { base: '.' })
        .pipe(remapIstanbul({
            reports: {
                'lcovonly': './coverage/remap/lcov.info',
                'json': './coverage/remap/coverage.json',
                'html': './coverage/remap/html-report',
                'cobertura': './coverage/remap/cobertura.xml'
            }
        }));
});

gulp.task('unit-test', () => {
    runSequence('test-server', 'remap-istanbul', function (err) {
        if (err) return process.exit(1);
        process.exit(0);
    });
});


